<template>
  <Tooltip title="反馈" placement="bottom" :mouseEnterDelay="0.5">
    <span @click="handleFeedback">
      <QuestionCircleOutlined />
    </span>
  </Tooltip>
</template>
<script lang="ts" setup>
  import { Tooltip } from 'ant-design-vue';
  import { useMessage } from '@/hooks/web/useMessage';

  import { QuestionCircleOutlined } from '@ant-design/icons-vue';

  const { createInfoModal } = useMessage();
  defineOptions({ name: 'Feedback' });
  function handleFeedback() {
    createInfoModal({ title: '反馈', content: '有任何建议或反馈，请随时联系@江梓帆~' });
  }
</script>
