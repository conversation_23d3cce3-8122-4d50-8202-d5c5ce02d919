import { getDatePickerCellValue } from './ADatePicker';
import {
  createCellRender,
  createEditR<PERSON>,
  createExportMethod,
  createFormItemRender,
} from './common';

export default {
  renderEdit: createEditRender(),
  renderCell: createCellRender(getDatePickerCellValue, () => {
    return ['YYYY-MM'];
  }),
  renderItemContent: createFormItemRender(),
  exportMethod: createExportMethod(getDatePickerCellValue, () => {
    return ['YYYY-MM'];
  }),
};
