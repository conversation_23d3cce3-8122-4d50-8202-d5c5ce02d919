import type { AppRouteModule } from '@/router/types';
import { RoleEnum } from '@/enums/roleEnum';
import { LAYOUT } from '@/router/constant';

const problem: AppRouteModule = {
  path: '/problem',
  name: 'problem',
  component: LAYOUT,
  redirect: '/problem/list',
  meta: {
    orderNo: 2,
    icon: 'material-symbols:problem-outline',
    title: '问题管理',
    // 超级管理员、管理员、内部用户可见
    roles: [RoleEnum.SUPER, RoleEnum.ADMIN, RoleEnum.INTERNALUSER],
  },
  children: [
    {
      path: 'dashboard',
      name: 'ProblemDashboard',
      meta: {
        title: '问题统计',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/problem/problem/dashboard/index.vue'),
    },
    {
      path: 'list',
      name: 'ProblemList',
      meta: {
        title: '问题列表',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/problem/problem/index.vue'),
    },

    {
      path: 'detail/:problemID',
      name: 'ProblemDetail',
      meta: {
        hideMenu: true,
        title: '问题详情',
        ignoreKeepAlive: false,
        showMenu: false,
        currentActiveMenu: '/problem/list',
      },
      component: () => import('@/views/problem/problem/ProblemDetail.vue'),
    },
    // {
    //   path: 'improvement-list',
    //   name: 'ProblemImprovementList',
    //   meta: {
    //     title: '问题整改措施',
    //     ignoreKeepAlive: true,
    //   },
    //   component: () => import('@/views/problem/improvement/index.vue'),
    // },
    // {
    //   path: 'detail/:improvementID',
    //   name: 'ProblemImprovementDetail',
    //   meta: {
    //     hideMenu: true,
    //     title: '整改措施详情',
    //     ignoreKeepAlive: true,
    //     showMenu: false,
    //     currentActiveMenu: '/problem/improvement-list',
    //   },
    //   component: () => import('@/views/problem/improvement/ImprovementDetail.vue'),
    // },
    // {
    //   path: 'my-problem',
    //   name: 'MyProblem',
    //   meta: {
    //     title: '我的工单',
    //     ignoreKeepAlive: true,
    //   },
    //   component: () => import('@/views/problem/report/index.vue'),
    // },
    // {
    //   path: 'todo-problem',
    //   name: 'TodoProblem',
    //   meta: {
    //     title: '待办工单',
    //     ignoreKeepAlive: true,
    //   },
    //   component: () => import('@/views/problem/report/index.vue'),
    // },
  ],
};

export default problem;
