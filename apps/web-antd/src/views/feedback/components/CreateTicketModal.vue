<script setup lang="ts">
import { computed, reactive, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import {
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Textarea,
} from '@vben-core/shadcn-ui';

import { createTicket } from '#/api/ticket';

// Emits
const emit = defineEmits<{
  (e: 'success'): void;
}>();
// 用户信息
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);

// 表单状态
const isSubmitting = ref(false);

// 表单数据
const formData = reactive({
  problemDescription: '',
  severityLevel: '个例问题',
  functionType: '',
  apps: 'TT语音',
  osType: '',
  mobileType: '',
  userTtid: '',
});

// 严重程度选项
const severityLevels = [
  { value: '个例问题', label: '个例问题' },
  { value: '批量问题', label: '批量问题' },
  { value: '紧急问题', label: '紧急问题' },
];

// 应用选项
const appOptions = [
  { value: 'TT语音', label: 'TT语音' },
  { value: 'TT直播', label: 'TT直播' },
  { value: 'TT游戏', label: 'TT游戏' },
];

// 操作系统选项
const osOptions = [
  { value: 'Android', label: 'Android' },
  { value: 'iOS', label: 'iOS' },
  { value: 'Windows', label: 'Windows' },
  { value: 'macOS', label: 'macOS' },
];

// 功能类型选项（简化版，实际应该从API获取）
const functionTypeOptions = [
  { value: '房间玩法', label: '房间玩法' },
  { value: '房间音频', label: '房间音频' },
  { value: '直播功能', label: '直播功能' },
  { value: '礼物系统', label: '礼物系统' },
  { value: '游戏功能', label: '游戏功能' },
  { value: '匹配系统', label: '匹配系统' },
  { value: '社交功能', label: '社交功能' },
  { value: '好友系统', label: '好友系统' },
  { value: '账号功能', label: '账号功能' },
  { value: '登录注册', label: '登录注册' },
];

// 创建VbenModal
const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
    resetForm();
  },
  onConfirm() {
    handleSubmit();
  },
});

// 提交表单
async function handleSubmit() {
  // 简单验证
  if (!formData.problemDescription.trim()) {
    console.warn('请填写问题描述');
    return;
  }

  try {
    isSubmitting.value = true;

    // 构造创建工单的数据
    const ticketData = {
      title: formData.problemDescription.slice(0, 50), // 使用问题描述的前50个字符作为标题
      text: formData.problemDescription,
      problemDescription: formData.problemDescription,
      severityLevel: formData.severityLevel,
      functionType: formData.functionType ? [formData.functionType] : [],
      apps: [formData.apps],
      osType: formData.osType ? [formData.osType] : [],
      mobileType: formData.mobileType ? [formData.mobileType] : [],
      userTtid: formData.userTtid ? [formData.userTtid] : [],
      creator:
        userInfo.value?.realName || userInfo.value?.username || 'Unknown',
      handler:
        userInfo.value?.realName || userInfo.value?.username || 'Unknown',
      stage: '待处理',
      startTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
      enterTime: new Date().toISOString().slice(0, 19).replace('T', ' '),
    };

    await createTicket(ticketData);

    // 重置表单
    resetForm();

    // 关闭对话框
    modalApi.close();

    // 通知父组件刷新数据
    emit('success');
  } catch (error) {
    console.error('创建工单失败:', error);
  } finally {
    isSubmitting.value = false;
  }
}

// 重置表单
function resetForm() {
  formData.problemDescription = '';
  formData.severityLevel = '个例问题';
  formData.functionType = '';
  formData.apps = 'TT语音';
  formData.osType = '';
  formData.mobileType = '';
  formData.userTtid = '';
}

// 暴露API给父组件
defineExpose({
  open: () => modalApi.open(),
  close: () => modalApi.close(),
});
</script>

<template>
  <Modal
    class="w-[600px]"
    title="创建用户反馈工单"
    description="请填写工单的基本信息，我们会尽快处理您的反馈。"
  >
    <div class="space-y-4">
      <!-- 问题描述 -->
      <div class="space-y-2">
        <Label class="text-sm font-medium">问题描述 *</Label>
        <Textarea
          v-model="formData.problemDescription"
          placeholder="请详细描述您遇到的问题..."
          class="min-h-[100px] resize-none"
          :disabled="isSubmitting"
        />
      </div>

      <!-- 严重程度 -->
      <div class="space-y-2">
        <Label class="text-sm font-medium">严重程度</Label>
        <Select v-model="formData.severityLevel" :disabled="isSubmitting">
          <SelectTrigger>
            <SelectValue placeholder="选择严重程度" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="option in severityLevels"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <!-- 功能类型 -->
      <div class="space-y-2">
        <Label class="text-sm font-medium">功能类型</Label>
        <Select v-model="formData.functionType" :disabled="isSubmitting">
          <SelectTrigger>
            <SelectValue placeholder="选择功能类型" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="option in functionTypeOptions"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div class="grid grid-cols-2 gap-4">
        <!-- 应用 -->
        <div class="space-y-2">
          <Label class="text-sm font-medium">应用</Label>
          <Select v-model="formData.apps" :disabled="isSubmitting">
            <SelectTrigger>
              <SelectValue placeholder="选择应用" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="option in appOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <!-- 操作系统 -->
        <div class="space-y-2">
          <Label class="text-sm font-medium">操作系统</Label>
          <Select v-model="formData.osType" :disabled="isSubmitting">
            <SelectTrigger>
              <SelectValue placeholder="选择操作系统" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem
                v-for="option in osOptions"
                :key="option.value"
                :value="option.value"
              >
                {{ option.label }}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-4">
        <!-- 设备型号 -->
        <div class="space-y-2">
          <Label class="text-sm font-medium">设备型号</Label>
          <Input
            v-model="formData.mobileType"
            placeholder="如：iPhone 14 Pro"
            :disabled="isSubmitting"
          />
        </div>

        <!-- 用户ID -->
        <div class="space-y-2">
          <Label class="text-sm font-medium">用户TTID</Label>
          <Input
            v-model="formData.userTtid"
            placeholder="用户的TTID"
            :disabled="isSubmitting"
          />
        </div>
      </div>
    </div>
  </Modal>
</template>
