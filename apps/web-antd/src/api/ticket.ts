import type { Ticket } from '#/views/feedback/list/data/tickets';

import { requestClient } from './request';

/**
 * 工单API路径枚举
 */
export enum TicketApi {
  // 指派工单
  assignTicket = '/feedback/assignTicket/',
  // 功能列表
  categoryList = '/ResponsiblePersons/categoryTree/',
  // 归档工单
  checkTicket = '/feedback/checkTicket/',
  // 完成工单
  completeTicket = '/feedback/completeTicket/',
  // 创建工单
  createTicket = '/feedback/createTicket/',
  // 删除工单
  deleteTicket = '/feedback/deleteTicket/',
  // 请求dify
  difyWorkflowsRun = '/dify/workflows/run',
  // 编辑工单
  editTicket = '/feedback/editTicket/',
  // 工单列表
  getTicketList = '/feedback/selectTicket/',
  // 认领工单
  handleTicket = '/feedback/handleTicket/',
  // 驳回工单
  rejectTicket = '/feedback/rejectTicket/',
  // 加急工单
  urgeTicket = '/feedback/urgeTicket/',
  // 人员列表
  userList = '/user/auth/userlist/',
}

/**
 * 工单API相关类型定义
 */
export namespace TicketApiTypes {
  /** 工单列表查询参数 */
  export interface GetTicketListParams {
    /** 页码 */
    page?: number;
    /** 每页数量 */
    limit?: number;
    /** 状态筛选 */
    status?: string;
    /** 处理人筛选 */
    handler?: string;
    /** 创建人筛选 */
    creator?: string;
    /** 标签筛选 */
    labels?: string[];
    /** 搜索关键词 */
    keyword?: string;
    /** 开始时间 */
    startTime?: string;
    /** 结束时间 */
    endTime?: string;
    /** 工单类型 */
    ticketType?: string;
  }

  /** 工单列表请求体格式 */
  export interface GetTicketListRequestBody {
    data: {
      limit: number;
      page: number;
      query: {
        creator?: string;
        endTime?: string;
        handler?: string;
        keyword?: string;
        labels?: string[];
        startTime?: string;
        status?: string;
        ticketType: string;
      };
    };
  }

  /** 工单列表响应 */
  export interface GetTicketListResult {
    /** 工单列表 */
    list: Ticket[];
    /** 总数 */
    total: number;
    /** 当前页 */
    page: number;
    /** 每页数量 */
    pageSize?: number;
    /** 每页数量（新格式） */
    limit?: number;
  }

  /** 创建工单参数 */
  export interface CreateTicketParams {
    /** 工单标题 */
    title: string;
    /** 问题描述 */
    text: string;
    /** 处理人 */
    handler: string;
    /** 严重程度 */
    severityLevel: string;
    /** 截止日期 */
    dueDate?: string;
    /** 标签 */
    labels?: string[];
    /** 附件 */
    attachments?: Array<{
      name: string;
      size?: string;
      type: string;
      url: string;
    }>;
    /** 应用信息 */
    apps?: string[];
    /** 应用版本 */
    appVersion?: string[];
    /** 操作系统类型 */
    osType?: string[];
    /** 设备类型 */
    mobileType?: string[];
    /** 问题分类 */
    firstLevelCategory?: string;
    /** 二级分类 */
    secondLevelCategory?: string;
  }

  /** 编辑工单参数 */
  export interface EditTicketParams {
    /** 工单ID */
    id: string;
    /** 工单标题 */
    title?: string;
    /** 问题描述 */
    text?: string;
    /** 状态 */
    status?: string;
    /** 处理人 */
    handler?: string;
    /** 严重程度 */
    severityLevel?: string;
    /** 截止日期 */
    dueDate?: string;
    /** 标签 */
    labels?: string[];
    /** 问题原因 */
    cause?: string;
    /** 处理结果 */
    result?: string;
    /** 附件 */
    attachments?: Array<{
      name: string;
      size?: string;
      type: string;
      url: string;
    }>;
  }

  /** 删除工单参数 */
  export interface DeleteTicketParams {
    /** 工单ID */
    id: string;
  }

  /** 认领工单参数 */
  export interface HandleTicketParams {
    /** 工单ID */
    ticketID: string;
    /** 应用信息 */
    apps?: string[];
  }

  /** 指派工单参数 */
  export interface AssignTicketParams {
    /** 工单ID */
    ticketID: string;
    /** 被指派人 */
    assignee: string[];
    /** 应用信息 */
    apps?: string[];
  }

  /** 完成工单参数 */
  export interface CompleteTicketParams {
    /** 工单ID */
    ticketID: string;
    /** 问题原因 */
    cause?: string;
    /** 处理结果 */
    result?: string;
    /** 是否需要发版解决 */
    hasClientRelease?: string;
  }

  /** 归档工单参数 */
  export interface CheckTicketParams {
    /** 工单ID */
    ticketID: string;
  }

  /** 驳回工单参数 */
  export interface RejectTicketParams {
    /** 工单ID */
    ticketID: string;
    /** 驳回原因 */
    reason?: string;
  }

  /** 加急工单参数 */
  export interface UrgeTicketParams {
    /** 工单ID */
    ticketID: string;
  }

  /** 功能分类选项 */
  export interface CategoryOption {
    /** 值 */
    value: string;
    /** 标签 */
    label: string;
    /** 子选项 */
    children?: CategoryOption[];
  }

  /** 用户选项 */
  export interface UserOption {
    /** 值 */
    value: string;
    /** 标签 */
    label: string;
  }

  /** 通用API响应 */
  export interface ApiResponse<T = any> {
    /** 状态码 */
    code: number;
    /** 响应消息 */
    message: string;
    /** 响应数据 */
    data: T;
    /** 是否成功 */
    success: boolean;
  }
}

/**
 * 获取工单列表
 * @param params 查询参数
 */
export async function getTicketList(
  params?: TicketApiTypes.GetTicketListParams,
) {
  // 构建新的请求体格式
  const requestBody: TicketApiTypes.GetTicketListRequestBody = {
    data: {
      page: params?.page || 1,
      limit: params?.limit || 10,
      query: {
        ticketType: params?.ticketType || 'allTicket',
        ...(params?.status && { status: params.status }),
        ...(params?.handler && { handler: params.handler }),
        ...(params?.creator && { creator: params.creator }),
        ...(params?.labels && { labels: params.labels }),
        ...(params?.keyword && { keyword: params.keyword }),
        ...(params?.startTime && { startTime: params.startTime }),
        ...(params?.endTime && { endTime: params.endTime }),
      },
    },
  };

  return requestClient.post<TicketApiTypes.GetTicketListResult>(
    TicketApi.getTicketList,
    requestBody,
  );
}

/**
 * 创建工单
 * @param data 工单数据
 */
export async function createTicket(data: TicketApiTypes.CreateTicketParams) {
  return requestClient.post<TicketApiTypes.ApiResponse<Ticket>>(
    TicketApi.createTicket,
    data,
  );
}

/**
 * 编辑工单
 * @param data 工单数据
 */
export async function editTicket(data: TicketApiTypes.EditTicketParams) {
  return requestClient.post<TicketApiTypes.ApiResponse<Ticket>>(
    TicketApi.editTicket,
    data,
  );
}

/**
 * 删除工单
 * @param params 删除参数
 */
export async function deleteTicket(params: TicketApiTypes.DeleteTicketParams) {
  return requestClient.post<TicketApiTypes.ApiResponse<boolean>>(
    TicketApi.deleteTicket,
    params,
  );
}

/**
 * 认领工单
 * @param params 认领参数
 */
export async function handleTicket(params: TicketApiTypes.HandleTicketParams) {
  return requestClient.post<TicketApiTypes.ApiResponse<boolean>>(
    TicketApi.handleTicket,
    params,
  );
}

/**
 * 指派工单
 * @param params 指派参数
 */
export async function assignTicket(params: TicketApiTypes.AssignTicketParams) {
  return requestClient.post<TicketApiTypes.ApiResponse<boolean>>(
    TicketApi.assignTicket,
    params,
  );
}

/**
 * 完成工单
 * @param params 完成参数
 */
export async function completeTicket(
  params: TicketApiTypes.CompleteTicketParams,
) {
  return requestClient.post<TicketApiTypes.ApiResponse<boolean>>(
    TicketApi.completeTicket,
    params,
  );
}

/**
 * 归档工单
 * @param params 归档参数
 */
export async function checkTicket(params: TicketApiTypes.CheckTicketParams) {
  return requestClient.post<TicketApiTypes.ApiResponse<boolean>>(
    TicketApi.checkTicket,
    params,
  );
}

/**
 * 驳回工单
 * @param params 驳回参数
 */
export async function rejectTicket(params: TicketApiTypes.RejectTicketParams) {
  return requestClient.post<TicketApiTypes.ApiResponse<boolean>>(
    TicketApi.rejectTicket,
    params,
  );
}

/**
 * 加急工单
 * @param params 加急参数
 */
export async function urgeTicket(params: TicketApiTypes.UrgeTicketParams) {
  return requestClient.post<TicketApiTypes.ApiResponse<boolean>>(
    TicketApi.urgeTicket,
    params,
  );
}

/**
 * 获取功能分类列表
 */
export async function getCategoryList() {
  return requestClient.get<
    TicketApiTypes.ApiResponse<TicketApiTypes.CategoryOption[]>
  >(TicketApi.categoryList);
}

/**
 * 获取用户列表
 */
export async function getUserList() {
  return requestClient.get<
    TicketApiTypes.ApiResponse<TicketApiTypes.UserOption[]>
  >(TicketApi.userList);
}

/**
 * 请求dify工作流 - 提取反馈信息
 */
export async function difyExtractFeedbackInfo(params: any) {
  return requestClient.post<TicketApiTypes.ApiResponse<any>>(
    `${TicketApi.difyWorkflowsRun}/extract_feedback_info/`,
    params,
  );
}
