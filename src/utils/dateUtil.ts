/**
 * Independent time operation tool to facilitate subsequent switch to dayjs
 */
import dayjs from 'dayjs';

const DATE_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
const DATE_FORMAT = 'YYYY-MM-DD';

export function formatToDateTime(date?: dayjs.ConfigType, format = DATE_TIME_FORMAT): string {
  return dayjs(date).format(format);
}

export function formatToDate(date?: dayjs.ConfigType, format = DATE_FORMAT): string {
  return dayjs(date).format(format);
}

export const dateUtil = dayjs;

export function getDuration(startTime, endTime) {
  console.log('startTime', startTime);
  console.log('endTime', endTime);

  if (startTime && endTime) {
    const st = new Date(startTime).getTime(); // 将 startsat 时间字符串转换为时间戳
    const et = new Date(endTime).getTime(); // 将 endsat 时间字符串转换为时间戳

    const diff = et - st; // 计算时间差（单位：毫秒）
    return Math.round(diff / 1000 / 60); // 将时间差转换为分钟数
  } else {
    return 0;
  }
}
