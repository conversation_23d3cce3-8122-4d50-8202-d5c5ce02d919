import type { AppRouteModule } from '@/router/types';
import { RoleEnum } from '@/enums/roleEnum';
import { LAYOUT } from '@/router/constant';

const improvement: AppRouteModule = {
  path: '/improvement',
  name: 'Improvement',
  component: LAYOUT,
  redirect: '/improvement/list',
  meta: {
    orderNo: 4,
    icon: 'icon-park-outline:transaction',
    title: '整改措施',
    // 超级管理员、管理员、内部用户可见
    roles: [RoleEnum.SUPER, RoleEnum.ADMIN, RoleEnum.INTERNALUSER],
  },
  children: [
    {
      path: 'list',
      name: 'ImprovementList',
      meta: {
        title: '整改措施列表',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/improvement/index.vue'),
    },
    {
      path: 'detail/:improvementID',
      name: 'ImprovementDetail',
      meta: {
        hideMenu: true,
        title: '整改措施详情',
        ignoreKeepAlive: false,
        showMenu: false,
        currentActiveMenu: '/improvement/list',
      },
      component: () => import('@/views/improvement/ImprovementDetail.vue'),
    },
    {
      path: 'check-list',
      name: 'CheckList',
      meta: {
        title: '检核项列表',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/improvement/checklist/index.vue'),
    },
    {
      path: 'check-list-configuration',
      name: 'CheckListConfiguration',
      meta: {
        title: '检核项配置',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/improvement/checklist/CheckListConfiguration.vue'),
    },
    // {
    //   path: 'my-improvement',
    //   name: 'MyProblem',
    //   meta: {
    //     title: '我的工单',
    //     ignoreKeepAlive: true,
    //   },
    //   component: () => import('@/views/improvement/report/index.vue'),
    // },
    // {
    //   path: 'todo-improvement',
    //   name: 'TodoProblem',
    //   meta: {
    //     title: '待办工单',
    //     ignoreKeepAlive: true,
    //   },
    //   component: () => import('@/views/improvement/report/index.vue'),
    // },
  ],
};

export default improvement;
