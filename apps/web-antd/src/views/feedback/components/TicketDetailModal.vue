<script setup lang="ts">
import type { Ticket } from '#/views/feedback/list/data/tickets';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import {
  Badge,
  Button,
  Separator,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@vben-core/shadcn-ui';

import {
  Calendar,
  Clock,
  ExternalLink,
  FileText,
  History,
  Paperclip,
  Smartphone,
  Tag,
  User,
} from 'lucide-vue-next';

import TicketActions from './TicketActions.vue';

// Emits
const emit = defineEmits<{
  (e: 'refresh'): void;
}>();

// 用户信息
const userStore = useUserStore();

// 创建VbenModal
const [Modal, modalApi] = useVbenModal({
  onCancel() {
    modalApi.close();
  },
});

// 当前工单数据
const currentTicket = ref<null | Ticket>(null);

// 格式化时间
function formatTime(time?: string) {
  if (!time) return '-';
  return time;
}

// 获取状态颜色
function getStatusColor(status?: string) {
  switch (status) {
    case '处理中': {
      return 'default';
    }
    case '已处理': {
      return 'default';
    } // 改为default，因为没有success variant
    case '已归档': {
      return 'outline';
    }
    case '待处理': {
      return 'secondary';
    }
    default: {
      return 'secondary';
    }
  }
}

// 获取严重程度颜色
function getSeverityColor(severity?: string) {
  switch (severity) {
    case '个例问题': {
      return 'secondary';
    }
    case '批量问题': {
      return 'default';
    }
    case '紧急问题': {
      return 'destructive';
    }
    default: {
      return 'secondary';
    }
  }
}

// 获取变更日志类型显示
function getChangeLogTypeLabel(type: string) {
  const typeMap: Record<string, string> = {
    create: '创建',
    appoint: '指派',
    resolved: '完成',
    archive: '归档',
    reject: '驳回',
    urge: '加急',
  };
  return typeMap[type] || type;
}

// 获取变更日志类型颜色
function getChangeLogTypeColor(type: string) {
  const colorMap: Record<
    string,
    'default' | 'destructive' | 'outline' | 'secondary'
  > = {
    create: 'default',
    appoint: 'secondary',
    resolved: 'default', // 改为default，因为没有success variant
    archive: 'outline',
    reject: 'destructive',
    urge: 'default',
  };
  return colorMap[type] || 'secondary';
}

// 处理附件下载
function handleDownloadAttachment(attachment: any) {
  if (attachment.url) {
    window.open(attachment.url, '_blank');
  }
}

// 暴露API给父组件
defineExpose({
  open: (ticket: Ticket) => {
    currentTicket.value = ticket;
    modalApi.open();
  },
  close: () => modalApi.close(),
});

// 处理工单操作后的刷新
function handleTicketActionRefresh() {
  emit('refresh');
  modalApi.close();
}
</script>

<template>
  <Modal
    class="max-h-[80vh] w-[900px]"
    :title="`工单详情 - ${currentTicket?.ticketID || currentTicket?.id}`"
  >
    <div v-if="currentTicket" class="space-y-6">
      <!-- 工单基本信息 -->
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <Badge :variant="getStatusColor(currentTicket.stage)">
              {{ currentTicket.stage }}
            </Badge>
            <Badge :variant="getSeverityColor(currentTicket.severityLevel)">
              {{ currentTicket.severityLevel }}
            </Badge>
          </div>
          <TicketActions
            :ticket="currentTicket"
            @refresh="handleTicketActionRefresh"
          />
        </div>

        <div class="space-y-2">
          <h3 class="text-lg font-semibold">
            {{ currentTicket.title || currentTicket.problemDescription }}
          </h3>
          <p class="text-muted-foreground text-sm">
            {{ currentTicket.problemDescription }}
          </p>
        </div>
      </div>

      <Separator />

      <!-- 详细信息标签页 -->
      <Tabs default-value="details" class="w-full">
        <TabsList class="grid w-full grid-cols-4">
          <TabsTrigger value="details">详细信息</TabsTrigger>
          <TabsTrigger value="timeline">处理历程</TabsTrigger>
          <TabsTrigger value="attachments">附件</TabsTrigger>
          <TabsTrigger value="technical">技术信息</TabsTrigger>
        </TabsList>

        <!-- 详细信息 -->
        <TabsContent value="details" class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <!-- 时间信息 -->
            <div class="space-y-3">
              <h4 class="flex items-center gap-2 font-medium">
                <Clock class="h-4 w-4" />
                时间信息
              </h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-muted-foreground">创建时间:</span>
                  <span>{{ formatTime(currentTicket.enterTime) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-muted-foreground">响应时间:</span>
                  <span>{{ formatTime(currentTicket.responseTime) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-muted-foreground">完成时间:</span>
                  <span>{{ formatTime(currentTicket.endTime) }}</span>
                </div>
              </div>
            </div>

            <!-- 人员信息 -->
            <div class="space-y-3">
              <h4 class="flex items-center gap-2 font-medium">
                <User class="h-4 w-4" />
                人员信息
              </h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-muted-foreground">创建人:</span>
                  <span>{{ currentTicket.creator || '-' }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-muted-foreground">处理人:</span>
                  <span>{{
                    currentTicket.handler || currentTicket.devProcessor || '-'
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-muted-foreground">响应人:</span>
                  <span>{{ currentTicket.respondent || '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 处理结果 -->
          <div
            v-if="currentTicket.cause || currentTicket.result"
            class="space-y-3"
          >
            <h4 class="flex items-center gap-2 font-medium">
              <FileText class="h-4 w-4" />
              处理结果
            </h4>
            <div class="space-y-3">
              <div v-if="currentTicket.cause">
                <label class="text-muted-foreground text-sm font-medium"
                  >问题原因:</label
                >
                <p class="bg-muted mt-1 rounded-md p-3 text-sm">
                  {{ currentTicket.cause }}
                </p>
              </div>
              <div v-if="currentTicket.result">
                <label class="text-muted-foreground text-sm font-medium"
                  >处理结果:</label
                >
                <p
                  class="bg-muted mt-1 whitespace-pre-wrap rounded-md p-3 text-sm"
                >
                  {{ currentTicket.result }}
                </p>
              </div>
            </div>
          </div>
        </TabsContent>

        <!-- 处理历程 -->
        <TabsContent value="timeline" class="space-y-4">
          <div class="space-y-4">
            <h4 class="flex items-center gap-2 font-medium">
              <History class="h-4 w-4" />
              处理历程
            </h4>
            <div class="space-y-3">
              <div
                v-for="(log, index) in currentTicket.changeLogs"
                :key="index"
                class="flex items-start gap-3 rounded-lg border p-3"
              >
                <Badge
                  :variant="getChangeLogTypeColor(log.type)"
                  class="mt-0.5"
                >
                  {{ getChangeLogTypeLabel(log.type) }}
                </Badge>
                <div class="flex-1 space-y-1">
                  <p class="text-sm">{{ log.content }}</p>
                  <div
                    class="text-muted-foreground flex items-center gap-2 text-xs"
                  >
                    <span>{{ log.time }}</span>
                    <span v-if="log.operator">操作人: {{ log.operator }}</span>
                    <span v-if="log.assignee"
                      >被指派人: {{ log.assignee }}</span
                    >
                    <span v-if="log.assignor">指派人: {{ log.assignor }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>

        <!-- 附件 -->
        <TabsContent value="attachments" class="space-y-4">
          <div class="space-y-4">
            <h4 class="flex items-center gap-2 font-medium">
              <Paperclip class="h-4 w-4" />
              附件列表
            </h4>
            <div
              v-if="
                currentTicket.attachments &&
                currentTicket.attachments.length > 0
              "
              class="space-y-2"
            >
              <div
                v-for="(attachment, index) in currentTicket.attachments"
                :key="index"
                class="flex items-center justify-between rounded-lg border p-3"
              >
                <div class="flex items-center gap-3">
                  <div class="bg-muted rounded p-2">
                    <Paperclip class="h-4 w-4" />
                  </div>
                  <div>
                    <p class="text-sm font-medium">{{ attachment.name }}</p>
                    <p class="text-muted-foreground text-xs">
                      {{ attachment.type }}
                      {{ attachment.size ? `• ${attachment.size}` : '' }}
                    </p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  @click="handleDownloadAttachment(attachment)"
                >
                  <ExternalLink class="mr-1 h-4 w-4" />
                  查看
                </Button>
              </div>
            </div>
            <div v-else class="text-muted-foreground py-8 text-center">
              暂无附件
            </div>
          </div>
        </TabsContent>

        <!-- 技术信息 -->
        <TabsContent value="technical" class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <!-- 应用信息 -->
            <div class="space-y-3">
              <h4 class="flex items-center gap-2 font-medium">
                <Smartphone class="h-4 w-4" />
                应用信息
              </h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-muted-foreground">应用:</span>
                  <span>{{ currentTicket.apps?.join(', ') || '-' }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-muted-foreground">版本:</span>
                  <span>{{ currentTicket.appVersion?.join(', ') || '-' }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-muted-foreground">操作系统:</span>
                  <span>{{ currentTicket.osType?.join(', ') || '-' }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-muted-foreground">设备型号:</span>
                  <span>{{ currentTicket.mobileType?.join(', ') || '-' }}</span>
                </div>
              </div>
            </div>

            <!-- 分类信息 -->
            <div class="space-y-3">
              <h4 class="flex items-center gap-2 font-medium">
                <Tag class="h-4 w-4" />
                分类信息
              </h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-muted-foreground">功能类型:</span>
                  <span>{{
                    currentTicket.functionType?.join(', ') || '-'
                  }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-muted-foreground">一级分类:</span>
                  <span>{{ currentTicket.firstLevelCategory || '-' }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-muted-foreground">二级分类:</span>
                  <span>{{ currentTicket.secondLevelCategory || '-' }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-muted-foreground">用户TTID:</span>
                  <span>{{ currentTicket.userTtid?.join(', ') || '-' }}</span>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  </Modal>
</template>
