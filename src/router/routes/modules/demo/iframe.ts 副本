import type { AppRouteModule } from '@/router/types';

import { LAYOUT } from '@/router/constant';
import { t } from '@/hooks/web/useI18n';

const IFrame = () => import('@/views/sys/iframe/FrameBlank.vue');

const iframe: AppRouteModule = {
  path: '/frame',
  name: 'Frame',
  component: LAYOUT,
  redirect: '/frame/doc',
  meta: {
    orderNo: 1000,
    icon: 'ion:tv-outline',
    title: '相关文档',
  },

  children: [
    {
      path: 'doc',
      name: 'Doc',
      component: IFrame,
      meta: {
        frameSrc: 'https://q9jvw0u5f5.feishu.cn/wiki/wikcnljxQ5X3tin7ioc9tTMHfPh#',
        title: '故障管理标准',
      },
    },
    // {
    //   path: 'https://doc.vvbin.cn/',
    //   name: 'DocExternal',
    //   component: IFrame,
    //   meta: {
    //     title: t('routes.demo.iframe.docExternal'),
    //   },
    // },
  ],
};

export default iframe;
