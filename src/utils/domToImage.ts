import domtoimage from 'dom-to-image';
import * as clipboard from 'clipboard-polyfill';

export function dom2img(node) {
  return domtoimage
    .toPng(node, { cacheBust: true })
    .then(function (dataUrl) {
      const img = new Image();
      img.src = dataUrl;
      document.body.appendChild(img);
      // const noPrefix = dataUrl.replace(/^data:image\/\w+;base64,/, ''); // replace + 正则 把前缀替换成空

      clip(dataUrl);
      return dataUrl;
    })
    .catch(function (error) {
      console.error('oops, dom2img went wrong!', error);
    });
}

function b64toBlob(b64Data, contentType = null, sliceSize = null) {
  contentType = contentType || 'image/png';
  sliceSize = sliceSize || 512;

  const byteCharacters = atob(b64Data);
  const byteArrays = [];
  for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
    const slice = byteCharacters.slice(offset, offset + sliceSize);
    const byteNumbers = new Array(slice.length);
    for (let i = 0; i < slice.length; i++) {
      byteNumbers[i] = slice.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    byteArrays.push(byteArray);
  }
  return new Blob(byteArrays, { type: contentType });
}

function clip(b64Data) {
  const item = navigator.clipboard.write([
    new ClipboardItem({
      'image/png': b64toBlob(b64Data.replace(/^data:image\/\w+;base64,/, ''), 'image/png', 512),
    }),
  ]);
}
