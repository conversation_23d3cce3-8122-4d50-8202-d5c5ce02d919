<script setup lang="ts">
import { computed, ref } from 'vue';
import { useVbenModal } from '@vben/common-ui';
import { useUserStore } from '@vben/stores';

import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@vben-core/shadcn-ui';

import { 
  assignTicket, 
  checkTicket, 
  completeTicket, 
  handleTicket, 
  rejectTicket, 
  urgeTicket 
} from '#/api/ticket';
import type { Ticket } from '#/views/feedback/list/data/tickets';

import { ChevronDown, Clock, User, CheckCircle, Archive, XCircle, AlertTriangle } from 'lucide-vue-next';

// Props
interface TicketActionsProps {
  ticket: Ticket;
}

const props = defineProps<TicketActionsProps>();

// Emits
const emit = defineEmits<{
  (e: 'refresh'): void;
}>();

// 用户信息
const userStore = useUserStore();
const userInfo = computed(() => userStore.userInfo);

// 加载状态
const loading = ref(false);

// 根据工单状态计算可用操作
const availableActions = computed(() => {
  const stage = props.ticket.stage;
  const actions = [];

  switch (stage) {
    case '待处理':
      actions.push(
        { key: 'handle', label: '认领', icon: User, variant: 'default' },
        { key: 'assign', label: '指派', icon: User, variant: 'outline' },
        { key: 'reject', label: '驳回', icon: XCircle, variant: 'destructive' },
      );
      break;
    case '处理中':
      actions.push(
        { key: 'complete', label: '完成', icon: CheckCircle, variant: 'default' },
        { key: 'assign', label: '重新指派', icon: User, variant: 'outline' },
        { key: 'reject', label: '驳回', icon: XCircle, variant: 'destructive' },
        { key: 'urge', label: '加急', icon: AlertTriangle, variant: 'secondary' },
      );
      break;
    case '已处理':
      actions.push(
        { key: 'check', label: '归档', icon: Archive, variant: 'default' },
        { key: 'reject', label: '驳回', icon: XCircle, variant: 'destructive' },
      );
      break;
    case '已归档':
      // 已归档的工单通常不允许操作
      break;
  }

  return actions;
});

// 处理工单操作
async function handleAction(action: string) {
  if (loading.value) return;
  
  loading.value = true;
  
  try {
    const ticketID = props.ticket.ticketID || props.ticket.id;
    
    switch (action) {
      case 'handle':
        await handleTicket({ 
          ticketID,
          apps: props.ticket.apps 
        });
        break;
      case 'assign':
        // 这里应该打开指派对话框，暂时使用默认指派
        await assignTicket({ 
          ticketID,
          assignee: [userInfo.value?.realName || userInfo.value?.username || 'Unknown'],
          apps: props.ticket.apps 
        });
        break;
      case 'complete':
        // 这里应该打开完成对话框，暂时使用默认完成
        await completeTicket({ 
          ticketID,
          cause: '问题已解决',
          result: '处理完成'
        });
        break;
      case 'check':
        await checkTicket({ ticketID });
        break;
      case 'reject':
        // 这里应该打开驳回对话框，暂时使用默认驳回
        await rejectTicket({ 
          ticketID,
          reason: '需要更多信息'
        });
        break;
      case 'urge':
        await urgeTicket({ ticketID });
        break;
    }
    
    // 操作成功后刷新数据
    emit('refresh');
  } catch (error) {
    console.error(`工单操作失败 (${action}):`, error);
  } finally {
    loading.value = false;
  }
}

// 获取操作按钮的样式
function getActionVariant(action: any) {
  return action.variant || 'outline';
}
</script>

<template>
  <div class="flex items-center gap-2">
    <!-- 主要操作按钮 -->
    <template v-if="availableActions.length > 0">
      <Button
        v-if="availableActions.length === 1"
        :variant="getActionVariant(availableActions[0])"
        size="sm"
        :disabled="loading"
        @click="handleAction(availableActions[0].key)"
      >
        <component :is="availableActions[0].icon" class="mr-1 h-4 w-4" />
        {{ availableActions[0].label }}
      </Button>
      
      <!-- 多个操作时使用下拉菜单 -->
      <DropdownMenu v-else>
        <DropdownMenuTrigger as-child>
          <Button variant="outline" size="sm" :disabled="loading">
            <Clock class="mr-1 h-4 w-4" />
            操作
            <ChevronDown class="ml-1 h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            v-for="action in availableActions"
            :key="action.key"
            @click="handleAction(action.key)"
          >
            <component :is="action.icon" class="mr-2 h-4 w-4" />
            {{ action.label }}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </template>
    
    <!-- 无可用操作时显示状态 -->
    <div v-else class="text-sm text-muted-foreground">
      {{ ticket.stage }}
    </div>
  </div>
</template>
