import type { AppRouteModule } from '@/router/types';
import { RoleEnum } from '@/enums/roleEnum';
import { LAYOUT } from '@/router/constant';
const IFrame = () => import('@/views/sys/iframe/FrameBlank.vue');

const fault: AppRouteModule = {
  path: '/fault',
  name: 'fault',
  component: LAYOUT,
  redirect: '/fault/list',
  meta: {
    orderNo: 3,
    icon: 'ion:file-tray-full-outline',
    title: '故障管理',
    // 超级管理员、管理员、内部用户可见
    roles: [RoleEnum.SUPER, RoleEnum.ADMIN, RoleEnum.INTERNALUSER],
  },
  children: [
    // {
    //   path: 'dashboard',
    //   name: 'FaultDashboard',
    //   meta: {
    //     title: '故障统计',
    //     ignoreKeepAlive: false,
    //   },
    //   component: () => import('@/views/problem/fault/dashboard/index.vue'),
    // },
    {
      path: 'list',
      name: 'FaultList',
      meta: {
        title: '故障列表',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/problem/fault/index.vue'),
    },
    {
      path: 'detail/:faultID',
      name: 'FaultDetail',
      meta: {
        hideMenu: true,
        title: '故障报告详情',
        ignoreKeepAlive: true,
        showMenu: false,
        currentActiveMenu: '/fault/list',
      },
      component: () => import('@/views/problem/fault/FaultDetail.vue'),
    },
    {
      path: 'https://q9jvw0u5f5.feishu.cn/wiki/wikcnljxQ5X3tin7ioc9tTMHfPh#FZ5D9a',
      name: 'GradingStandards',
      component: IFrame,
      meta: {
        title: '定级标准',
      },
    },
    // {
    //   path: 'improvement-list',
    //   name: 'FaultImprovementList',
    //   meta: {
    //     title: '故障整改措施',
    //     ignoreKeepAlive: true,
    //   },
    //   component: () => import('@/views/fault/improvement/index.vue'),
    // },
    // {
    //   path: 'detail/:improvementID',
    //   name: 'FaultImprovementDetail',
    //   meta: {
    //     hideMenu: true,
    //     title: '整改措施详情',
    //     ignoreKeepAlive: true,
    //     showMenu: false,
    //     currentActiveMenu: '/fault/improvement-list',
    //   },
    //   component: () => import('@/views/fault/improvement/ImprovementDetail.vue'),
    // },
    // {
    //   path: 'my-fault',
    //   name: 'MyFault',
    //   meta: {
    //     title: '我的工单',
    //     ignoreKeepAlive: true,
    //   },
    //   component: () => import('@/views/fault/report/index.vue'),
    // },
    // {
    //   path: 'todo-fault',
    //   name: 'TodoFault',
    //   meta: {
    //     title: '待办工单',
    //     ignoreKeepAlive: true,
    //   },
    //   component: () => import('@/views/fault/report/index.vue'),
    // },
  ],
};

export default fault;
