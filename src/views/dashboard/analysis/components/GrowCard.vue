<template>
  <div class="md:flex">
    <template v-for="(item, index) in growCardList" :key="item.title">
      <Card
        size="small"
        :loading="loading"
        :title="item.title"
        class="md:w-1/5 w-full !md:mt-0"
        :class="{ '!md:mr-4': index + 1 < 5, '!mt-4': index > 0 }"
      >
        <!-- <template #extra>
          <Tag :color="item.color">{{ item.action }}</Tag>
        </template> -->

        <div class="py-4 px-4 flex justify-between items-center">
          <CountTo :startVal="0" :endVal="item.value" class="text-2xl" />
          <Icon :icon="item.icon" :size="40" />
        </div>

        <!-- <div class="p-2 px-4 flex justify-between">
          <span>总{{ item.title }}</span>
          <CountTo :startVal="1" :endVal="item.total" />
        </div> -->
      </Card>
    </template>
  </div>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { CountTo } from '@/components/CountTo';
  import Icon from '@/components/Icon/Icon.vue';
  import { useRequest } from '@vben/hooks';
  import { getTicketList } from '@/api/feedback/feedback';
  import { Tag, Card } from 'ant-design-vue';
  import { clone } from 'xe-utils';

  const loading = ref(false);
  const ticketAllCount = ref();
  const ticketPendingCount = ref();
  const ticketProcessingCount = ref();
  const ticketFinishCount = ref();

  const growCardList = ref([
    {
      title: '总工单数',
      icon: 'all-ticket|svg',
      value: 0,
      color: 'green',
    },
    {
      title: '待处理',
      icon: 'handle-ticket|svg',
      value: 0,
      color: 'blue',
    },
    {
      title: '处理中',
      icon: 'processing-ticket|svg',
      value: 0,
      color: 'orange',
    },
    {
      title: '已处理',
      icon: 'done-ticket|svg',
      value: 0,
      color: 'orange',
    },
    {
      title: '已归档',
      icon: 'finish-ticket|svg',
      value: 0,
      color: 'purple',
    },
  ]);
  const totalList = clone(growCardList);

  useRequest(getTicketList, {
    defaultParams: [{ query: { stage: '' } }],
    onBefore: () => {
      loading.value = true;
    },
    onSuccess: (result) => {
      if (result) {
        growCardList.value[0].value = result.total;
      }
    },
    onFinally: () => {
      loading.value = false;
    },
  });
  useRequest(getTicketList, {
    defaultParams: [{ query: { stage: '待处理' } }],
    onBefore: () => {
      loading.value = true;
    },
    onSuccess: (result) => {
      if (result) {
        growCardList.value[1].value = result.total;
      }
    },
    onFinally: () => {
      loading.value = false;
    },
  });
  useRequest(getTicketList, {
    defaultParams: [{ query: { stage: '处理中' } }],
    onBefore: () => {
      loading.value = true;
    },
    onSuccess: (result) => {
      if (result) {
        growCardList.value[2].value = result.total;
      }
    },
    onFinally: () => {
      loading.value = false;
    },
  });
  useRequest(getTicketList, {
    defaultParams: [{ query: { stage: '已处理' } }],
    onBefore: () => {
      loading.value = true;
    },
    onSuccess: (result) => {
      if (result) {
        growCardList.value[3].value = result.total;
      }
    },
    onFinally: () => {
      loading.value = false;
    },
  });
  useRequest(getTicketList, {
    defaultParams: [{ query: { stage: '已归档' } }],
    onBefore: () => {
      loading.value = true;
    },
    onSuccess: (result) => {
      if (result) {
        growCardList.value[4].value = result.total;
      }
    },
    onFinally: () => {
      loading.value = false;
    },
  });
</script>
