import type { AppRouteModule } from '@/router/types';

import { LAYOUT } from '@/router/constant';
import { RoleEnum } from '@/enums/roleEnum';
// import ticket from 'mock/feedback/ticket';
const IFrame = () => import('@/views/sys/iframe/FrameBlank.vue');

const feedback: AppRouteModule = {
  path: '/feedback',
  name: 'Feedback',
  component: LAYOUT,
  redirect: '/feedback/ticket-list',
  meta: {
    orderNo: 0,
    icon: 'codicon:feedback',
    title: '用户反馈（TT）',
    roles: [RoleEnum.SUPER, RoleEnum.ADMIN, RoleEnum.INTERNALUSER, RoleEnum.EXTERNALUSER],
  },
  children: [
    {
      path: 'feedbackDashboard',
      name: 'feedbackDashboard',
      component: () => import('@/views/feedback/dashboard/analysis/index.vue'),
      meta: {
        // affix: true,
        title: '工单统计',
      },
    },
    {
      path: 'ticket-list',
      name: 'TicketList',
      meta: {
        title: '工单列表',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/feedback/ticket/index.vue'),
    },
    {
      path: 'my-ticket',
      name: 'MyTicket',
      meta: {
        title: '我的工单',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/feedback/ticket/index.vue'),
    },
    {
      path: 'todo-ticket',
      name: 'TodoTicket',
      meta: {
        title: '待办工单',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/feedback/ticket/index.vue'),
    },
    {
      path: 'ticket-detail/:ticketID',
      name: 'TicketDetail',
      meta: {
        hideMenu: true,
        title: '工单详情',
        ignoreKeepAlive: false,
        showMenu: false,
        // currentActiveMenu: '/feedback/ticket-list',
      },
      component: () => import('@/views/feedback/ticket/TicketDetail.vue'),
    },
    {
      path: 'https://yw-cmdb.ttyuyin.com/#/function-relation',
      name: 'FunctionRelation',
      component: IFrame,
      meta: {
        title: '功能关系',
      },
    },
  ],
};

export default feedback;
