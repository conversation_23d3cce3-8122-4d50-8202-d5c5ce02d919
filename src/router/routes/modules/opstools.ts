import type { AppRouteModule } from '@/router/types';
import { RoleEnum } from '@/enums/roleEnum';
import { LAYOUT } from '@/router/constant';

const opsTools: AppRouteModule = {
  path: '/ops-tools',
  name: 'ops-tools',
  component: LAYOUT,
  redirect: '/ops-tools/rtmp-player',
  meta: {
    // 开发阶段隐藏菜单
    // hideMenu: true,

    orderNo: 5,
    icon: 'iconoir:tools',
    title: '工具库',
    // 超级管理员、管理员、内部用户可见
    roles: [RoleEnum.SUPER, RoleEnum.ADMIN, RoleEnum.INTERNALUSER],
  },
  children: [
    {
      path: 'rtmp-player',
      name: 'RtmpPlayer',
      meta: {
        title: 'RTMP在线播放',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/ops-tools/RTMPPlayer.vue'),
    },
  ],
};

export default opsTools;
