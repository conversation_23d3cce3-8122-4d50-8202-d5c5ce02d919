// 数字人用户反馈
import type { AppRouteModule } from '@/router/types';
import { RoleEnum } from '@/enums/roleEnum';

import { LAYOUT } from '@/router/constant';
// import ticket from 'mock/feedback/ticket';
const IFrame = () => import('@/views/sys/iframe/FrameBlank.vue');

const feedbackX: AppRouteModule = {
  path: '/feedbackX',
  name: 'feedbackX',
  component: LAYOUT,
  redirect: '/feedback-x/ticket-list',
  meta: {
    orderNo: 1,
    icon: 'codicon:feedback',
    title: '用户反馈（数字人）',
    // 超级管理员、管理员、内部用户可见
    roles: [RoleEnum.SUPER, RoleEnum.ADMIN, RoleEnum.INTERNALUSER],
  },

  children: [
    {
      path: 'feedbackDashboard',
      name: 'feedbackXDashboard',
      component: () => import('@/views/feedbackX/dashboard/analysis/index.vue'),
      meta: {
        // affix: true,
        title: '工单统计',
      },
    },
    {
      path: 'ticket-list',
      name: 'TicketListX',
      meta: {
        title: '工单列表',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/feedbackX/ticket/index.vue'),
    },
    {
      path: 'my-ticket',
      name: 'MyTicketX',
      meta: {
        title: '我的工单',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/feedbackX/ticket/index.vue'),
    },
    {
      path: 'todo-ticket',
      name: 'TodoTicketX',
      meta: {
        title: '待办工单',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/feedbackX/ticket/index.vue'),
    },
    {
      path: 'ticket-detail/:ticketID',
      name: 'TicketDetailX',
      meta: {
        hideMenu: true,
        title: '工单详情',
        ignoreKeepAlive: false,
        showMenu: false,
        // currentActiveMenu: '/feedback/ticket-list',
      },
      component: () => import('@/views/feedbackX/ticket/TicketDetail.vue'),
    },
    {
      path: 'https://yw-cmdb.ttyuyin.com/#/function-relation',
      name: 'FunctionRelation',
      component: IFrame,
      meta: {
        title: '功能关系',
      },
    },
  ],
};

export default feedbackX;
