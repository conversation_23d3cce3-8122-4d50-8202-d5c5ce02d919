<template>
  <div class="h-full flex justify-center">
    <div ref="domRef" class=""></div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, onUnmounted, ref, watchEffect } from 'vue';
  import Player from 'xgplayer';
  import 'xgplayer/dist/index.min.css';
  import { propTypes } from '@/utils/propTypes';

  const props = defineProps({
    url: propTypes.string.def(''),
    id: propTypes.string.def(''),
  });

  const domRef = ref<HTMLElement>();
  const player = ref<Player>();

  function renderXgPlayer() {
    if (!domRef.value) return;
    player.value = new Player({
      width: '360px',
      id: props.id,
      el: domRef.value,
      url: props.url,
      fluid: false,
      hidePortrait: true,
    });
  }
  function destroyXgPlayer() {
    player.value?.destroy();
  }

  onMounted(() => {
    renderXgPlayer();
  });

  onUnmounted(() => {
    destroyXgPlayer();
  });
  watchEffect(() => {
    renderXgPlayer();
  });
</script>

<style scoped></style>
