<script lang="ts"></script>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
  Button,
  Separator,
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '@vben-core/shadcn-ui';

import { Plus } from 'lucide-vue-next';

import { useTicketData } from '#/views/feedback/list/composables/useTicketData';

import AppSidebar from './list/components/AppSidebar.vue';
import Layout from './list/Layout.vue';
import CreateTicketModal from './components/CreateTicketModal.vue';

// 接收路由传递的props
defineProps({
  activeMenu: {
    type: String,
    default: 'all',
  },
  id: {
    type: String,
    default: '',
  },
});

// 开发环境下导入测试API
if (import.meta.env.DEV) {
  import('./test-api');
}

const route = useRoute();
const currentActiveMenu = ref('all');

// 使用工单数据管理
const { tickets, loading, error, fetchTickets, refreshTickets } =
  useTicketData();

// 创建工单模态框引用
const createModalRef = ref();

// 创建事件处理函数
const handleMenuClick = ((event: CustomEvent) => {
  const { url } = event.detail;
  updateMenuFromPath(url);
}) as EventListener;

onMounted(async () => {
  updateMenuFromPath(route.path);

  // 监听自定义菜单点击事件
  window.addEventListener('menu-click', handleMenuClick);

  // 获取工单数据
  await fetchTickets();
});

// 组件卸载时移除事件监听器
onUnmounted(() => {
  window.removeEventListener('menu-click', handleMenuClick);
});

// 监听路由变化，更新当前活动菜单
watch(
  () => route.path,
  (newPath) => {
    updateMenuFromPath(newPath);
  },
);

// 根据路径更新菜单
function updateMenuFromPath(path: string) {
  if (path.includes('/feedback/created')) {
    currentActiveMenu.value = 'created';
  } else if (path.includes('/feedback/todo')) {
    currentActiveMenu.value = 'todo';
  } else if (path.includes('/feedback/ticket-detail')) {
    // 对于工单详情页，保持当前的菜单状态，不改变
    // currentActiveMenu.value 保持不变
  } else {
    // 对于所有工单页面，显示为 'all'
    currentActiveMenu.value = 'all';
  }
}

// 处理创建工单
function handleCreateTicket() {
  createModalRef.value?.open();
}

// 处理创建工单成功
function handleCreateSuccess() {
  // 刷新工单列表
  refreshTickets();
}

// 处理工单数据刷新
function handleRefreshTickets() {
  refreshTickets();
}
</script>

<template>
  <div>
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header
          class="flex h-12 shrink-0 items-center justify-between gap-2 border-b"
        >
          <div class="flex items-center gap-2 px-4">
            <SidebarTrigger class="-ml-1" />
            <Separator orientation="vertical" class="mr-2 h-4" />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem class="hidden md:block">
                  <BreadcrumbLink href="/feedback"> 用户反馈 </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator class="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>
                    {{
                      currentActiveMenu === 'created'
                        ? '我创建的'
                        : currentActiveMenu === 'todo'
                          ? '我待办的'
                          : '所有工单'
                    }}
                  </BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>

          <!-- 操作按钮区域 -->
          <div class="flex items-center gap-2 px-4">
            <Button
              @click="handleCreateTicket"
              class="bg-primary text-primary-foreground hover:bg-primary/90 h-8 px-3 text-sm"
            >
              <Plus class="mr-1 h-4 w-4" />
              创建工单
            </Button>
          </div>
        </header>

        <!-- 加载状态 -->
        <div v-if="loading" class="flex h-96 items-center justify-center">
          <div class="text-center">
            <div class="text-muted-foreground mb-2 text-sm">
              正在加载工单数据...
            </div>
            <div
              class="border-primary mx-auto h-8 w-8 animate-spin rounded-full border-b-2"
            ></div>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="flex h-96 items-center justify-center">
          <div class="max-w-md text-center">
            <div class="text-destructive mb-2 text-sm">加载工单数据失败</div>
            <div class="text-muted-foreground mb-4 text-xs">{{ error }}</div>

            <!-- 如果是用户未登录错误 -->
            <div
              v-if="error.includes('用户未登录')"
              class="mb-4 rounded-md border border-orange-200 bg-orange-50 p-4"
            >
              <div class="mb-2 text-sm text-orange-800">
                <strong>用户未登录：</strong>系统将自动跳转到登录页面
              </div>
              <div class="mb-3 text-xs text-orange-600">
                如果没有自动跳转，请手动点击下方按钮前往登录
              </div>
              <button
                @click="$router.push('/auth/login')"
                class="mr-2 inline-block rounded bg-orange-500 px-3 py-1 text-xs text-white hover:bg-orange-600"
              >
                前往登录页面
              </button>
            </div>

            <!-- Token无效错误 -->
            <div
              v-else-if="error.includes('Token无效')"
              class="mb-4 rounded-md border border-red-200 bg-red-50 p-4"
            >
              <div class="mb-2 text-sm text-red-800">
                <strong>Token无效：</strong>需要重新登录
              </div>
              <div class="mb-3 text-xs text-red-600">
                您的登录状态已过期，请重新登录
              </div>
              <button
                @click="$router.push('/auth/login')"
                class="inline-block rounded bg-red-500 px-3 py-1 text-xs text-white hover:bg-red-600"
              >
                重新登录
              </button>
            </div>

            <!-- 其他认证错误 -->
            <div
              v-else-if="error.includes('认证失败')"
              class="mb-4 rounded-md border border-blue-200 bg-blue-50 p-4"
            >
              <div class="mb-2 text-sm text-blue-800">
                <strong>提示：</strong>需要先登录测试服务器
              </div>
              <div class="mb-3 text-xs text-blue-600">
                请在新标签页中打开测试服务器并登录，然后回到此页面重试
              </div>
              <a
                href="http://itmp-test.ttyuyin.com"
                target="_blank"
                class="inline-block rounded bg-blue-500 px-3 py-1 text-xs text-white hover:bg-blue-600"
              >
                打开测试服务器 ↗
              </a>
            </div>

            <button
              @click="refreshTickets"
              class="bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm"
            >
              重试
            </button>
          </div>
        </div>

        <!-- 工单列表 -->
        <Layout
          v-else
          :tickets="tickets"
          :nav-collapsed-size="4"
          :active-menu="currentActiveMenu"
          :selected-ticket-id="id"
          @update:active-menu="currentActiveMenu = $event"
          @refresh="handleRefreshTickets"
        />
      </SidebarInset>
    </SidebarProvider>

    <!-- 创建工单模态框 -->
    <CreateTicketModal ref="createModalRef" @success="handleCreateSuccess" />
  </div>
</template>
