// 唱鸭用户反馈
import type { AppRouteModule } from '@/router/types';
import { RoleEnum } from '@/enums/roleEnum';

import { LAYOUT } from '@/router/constant';
// import ticket from 'mock/feedback/ticket';
const IFrame = () => import('@/views/sys/iframe/FrameBlank.vue');

const feedbackCY: AppRouteModule = {
  path: '/feedback/cy',
  name: 'feedbackCY',
  component: LAYOUT,
  redirect: '/feedback/cy/ticket-list',
  meta: {
    orderNo: 1,
    icon: 'codicon:feedback',
    title: '用户反馈（唱鸭）',
    // 超级管理员、管理员、内部用户可见
    roles: [RoleEnum.SUPER, RoleEnum.ADMIN, RoleEnum.INTERNALUSER, RoleEnum.EXTERNALUSER],
  },

  children: [
    {
      path: 'feedbackDashboard',
      name: 'feedbackCYDashboard',
      component: () => import('@/views/feedbackCY/dashboard/analysis/index.vue'),
      meta: {
        // affix: true,
        title: '工单统计',
      },
    },
    {
      path: 'ticket-list',
      name: 'TicketListCY',
      meta: {
        title: '工单列表',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/feedbackCY/ticket/index.vue'),
    },
    {
      path: 'my-ticket',
      name: 'MyTicketCY',
      meta: {
        title: '我的工单',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/feedbackCY/ticket/index.vue'),
    },
    {
      path: 'todo-ticket',
      name: 'TodoTicketCY',
      meta: {
        title: '待办工单',
        ignoreKeepAlive: false,
      },
      component: () => import('@/views/feedbackCY/ticket/index.vue'),
    },
    {
      path: 'ticket-detail/:ticketID',
      name: 'TicketDetailCY',
      meta: {
        hideMenu: true,
        title: '工单详情',
        ignoreKeepAlive: false,
        showMenu: false,
        // currentActiveMenu: '/feedback/ticket-list',
      },
      component: () => import('@/views/feedbackCY/ticket/TicketDetail.vue'),
    },
    {
      path: 'https://yw-cmdb.ttyuyin.com/#/function-relation',
      name: 'FunctionRelation',
      component: IFrame,
      meta: {
        title: '功能关系',
      },
    },
  ],
};

export default feedbackCY;
