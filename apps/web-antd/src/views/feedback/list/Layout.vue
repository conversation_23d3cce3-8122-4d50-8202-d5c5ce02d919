<script lang="ts" setup>
import type { Ticket } from './data/tickets';

import { computed, nextTick, onMounted, ref, watch } from 'vue';

import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
  Separator,
  TooltipProvider,
} from '@vben-core/shadcn-ui';

import TicketFilterToolbar from './components/TicketFilterToolbar.vue';
import TicketDisplay from './Display.vue';
import TicketList from './List.vue';
import AdvancedSearch from '../components/AdvancedSearch.vue';
import TicketDetailModal from '../components/TicketDetailModal.vue';

interface TicketProps {
  tickets: Ticket[];
  defaultLayout?: number[];
  defaultCollapsed?: boolean;
  navCollapsedSize: number;
  activeMenu: string;
  selectedTicketId?: string;
}

// 不再需要LinkProp接口

const props = withDefaults(defineProps<TicketProps>(), {
  defaultCollapsed: false,
  defaultLayout: () => [26, 30, 70],
  selectedTicketId: '',
});

// 定义 emit 事件
const emit = defineEmits<{
  (e: 'update:activeMenu', menu: string): void;
  (e: 'refresh'): void;
}>();

// 使用 computed 来确保 tickets 响应 props.tickets 的变化
const tickets = computed(() => props.tickets);
// 不再需要router

// 不再需要isCollapsed变量
const selectedTicket = ref<string | undefined>();
// 初始化选中的工单，优先使用传入的工单ID
if (props.selectedTicketId) {
  selectedTicket.value = props.selectedTicketId;
} else if (props.tickets && props.tickets.length > 0 && props.tickets[0]) {
  selectedTicket.value = props.tickets[0].id;
}
const searchValue = ref('');
const isMobile = ref(false);

// 筛选条件
const activeFilters = ref<Record<string, string[]>>({});

// 高级搜索条件
const searchFilters = ref<Record<string, any>>({});

// 工单详情模态框引用
const ticketDetailModalRef = ref();

onMounted(() => {
  // 设置移动设备检测
  isMobile.value = window.innerWidth < 768;
  window.addEventListener('resize', () => {
    isMobile.value = window.innerWidth < 768;
  });

  // 确保selectedMenu与props.activeMenu同步
  if (props.activeMenu) {
    selectedMenu.value = props.activeMenu;
  }

  // 组件挂载后，如果有指定的工单ID，确保它被正确选中
  if (props.selectedTicketId && tickets.value && tickets.value.length > 0) {
    const ticketExists = tickets.value.some(
      (item) =>
        item.id === props.selectedTicketId ||
        item.id.toString() === props.selectedTicketId ||
        props.selectedTicketId === item.id.toString(),
    );
    if (ticketExists) {
      selectedTicket.value = props.selectedTicketId;
      nextTick(() => {
        ensureTicketVisible(props.selectedTicketId!);
      });
    }
  }
});

// 监听activeMenu属性变化，更新selectedMenu
watch(
  () => props.activeMenu,
  (newValue) => {
    if (newValue) {
      selectedMenu.value = newValue;
    }
  },
);

// 监听selectedTicketId属性变化，更新selectedTicket
watch(
  () => props.selectedTicketId,
  (newValue) => {
    if (newValue && tickets.value && tickets.value.length > 0) {
      // 检查该ID的工单是否存在（支持字符串和数字类型匹配）
      const ticketExists = tickets.value.some(
        (item) =>
          item.id === newValue ||
          item.id.toString() === newValue ||
          newValue === item.id.toString(),
      );
      if (ticketExists) {
        selectedTicket.value = newValue;

        // 使用 nextTick 确保所有计算属性都已更新
        nextTick(() => {
          ensureTicketVisible(newValue);
        });
      }
    }
  },
  { immediate: true },
);
const debouncedSearch = computed(() => searchValue.value);

// 处理筛选事件
function handleFilter(filters: Record<string, string[]>) {
  activeFilters.value = filters;
}

// 处理高级搜索
function handleAdvancedSearch(filters: Record<string, any>) {
  searchFilters.value = filters;
  // 如果有关键词搜索，同时更新searchValue
  if (filters.keyword) {
    searchValue.value = filters.keyword;
  }
}

// 重置高级搜索
function handleResetSearch() {
  searchFilters.value = {};
  searchValue.value = '';
}

// 打开工单详情
function handleOpenTicketDetail(ticket: Ticket) {
  ticketDetailModalRef.value?.open(ticket);
}

// 刷新工单数据
function handleRefreshTickets() {
  // 这里应该调用父组件的刷新方法
  // 暂时先触发一个事件
  emit('refresh');
}

// 是否有活跃的筛选条件
const hasActiveFilters = computed(() => {
  return Object.values(activeFilters.value).some(
    (arr) => Array.isArray(arr) && arr.length > 0,
  );
});

// 应用筛选条件的工单列表
const filteredByFilters = computed(() => {
  // 如果没有筛选条件，返回所有工单
  if (!hasActiveFilters.value) {
    return tickets.value;
  }

  // 应用筛选条件
  return tickets.value.filter((item) => {
    // 检查每个筛选类别
    for (const [key, values] of Object.entries(activeFilters.value)) {
      // 如果该类别没有选择任何值，跳过
      if (!Array.isArray(values) || values.length === 0) continue;

      // 根据不同的字段类型进行筛选
      if (key === 'labels' || key === 'apps') {
        // 数组字段：检查是否有交集
        const itemValues = (item[key as keyof typeof item] as string[]) || [];
        if (!values.some((v) => itemValues.includes(v))) {
          return false;
        }
      } else {
        // 字符串字段：检查是否匹配
        const itemValue = item[key as keyof typeof item] as string;
        if (!itemValue || !values.includes(itemValue)) {
          return false;
        }
      }
    }

    // 所有条件都满足
    return true;
  });
});

// 应用高级搜索的工单列表
const filteredByAdvancedSearch = computed(() => {
  let output: Ticket[] = filteredByFilters.value;

  // 如果没有高级搜索条件，只应用基本搜索
  if (Object.keys(searchFilters.value).length === 0) {
    const searchValue = debouncedSearch.value?.trim();
    if (searchValue) {
      output = output.filter((item) => {
        return (
          item.id.toLowerCase().includes(searchValue.toLowerCase()) ||
          (item.ticketID || '')
            .toLowerCase()
            .includes(searchValue.toLowerCase()) ||
          (item.title || '')
            .toLowerCase()
            .includes(searchValue.toLowerCase()) ||
          (item.text || '').toLowerCase().includes(searchValue.toLowerCase()) ||
          (item.problemDescription || '')
            .toLowerCase()
            .includes(searchValue.toLowerCase()) ||
          (item.date || '')
            .toString()
            .toLowerCase()
            .includes(searchValue.toLowerCase()) ||
          (item.status || item.stage || '')
            .toLowerCase()
            .includes(searchValue.toLowerCase()) ||
          (item.labels || item.functionType || []).some((label) =>
            label.toLowerCase().includes(searchValue.toLowerCase()),
          ) ||
          (item.creator || '')
            .toLowerCase()
            .includes(searchValue.toLowerCase()) ||
          (item.handler || item.devProcessor || '')
            .toLowerCase()
            .includes(searchValue.toLowerCase())
        );
      });
    }
    return output;
  }

  // 应用高级搜索条件
  return output.filter((item) => {
    for (const [key, value] of Object.entries(searchFilters.value)) {
      if (!value || value === '') continue;

      switch (key) {
        case 'keyword':
          const keyword = value.toLowerCase();
          const matchesKeyword =
            item.id.toLowerCase().includes(keyword) ||
            (item.ticketID || '').toLowerCase().includes(keyword) ||
            (item.title || '').toLowerCase().includes(keyword) ||
            (item.text || '').toLowerCase().includes(keyword) ||
            (item.problemDescription || '').toLowerCase().includes(keyword);
          if (!matchesKeyword) return false;
          break;
        case 'status':
          if ((item.status || item.stage) !== value) return false;
          break;
        case 'severityLevel':
          if (item.severityLevel !== value) return false;
          break;
        case 'functionType':
          if (!item.functionType?.includes(value)) return false;
          break;
        case 'creator':
          if (item.creator !== value) return false;
          break;
        case 'handler':
          if ((item.handler || item.devProcessor) !== value) return false;
          break;
        case 'apps':
          if (!item.apps?.includes(value)) return false;
          break;
        case 'osType':
          if (!item.osType?.includes(value)) return false;
          break;
        case 'startTime':
          if (item.enterTime && item.enterTime < value) return false;
          break;
        case 'endTime':
          if (item.enterTime && item.enterTime > value) return false;
          break;
      }
    }
    return true;
  });
});

// 应用搜索的工单列表（保持向后兼容）
const filteredTicketList = computed(() => filteredByAdvancedSearch.value);

// 使用props.activeMenu作为初始值
const selectedMenu = ref(props.activeMenu || 'all');
const ticketLists = {
  // 所有工单
  all: computed(() => filteredTicketList.value),
  // 我创建的
  created: computed(() =>
    filteredTicketList.value.filter(
      (item) => item.creator?.toLowerCase() === 'admin',
    ),
  ),
  // 我待办的
  todo: computed(() =>
    filteredTicketList.value.filter(
      (item) => item.handler?.toLowerCase() === 'admin',
    ),
  ),
  // 待处理
  pending: computed(() =>
    filteredTicketList.value.filter(
      (item) => (item.stage || item.status) === '待处理',
    ),
  ),
  // 处理中
  processing: computed(() =>
    filteredTicketList.value.filter(
      (item) => (item.stage || item.status) === '处理中',
    ),
  ),
  // 已处理
  done: computed(() =>
    filteredTicketList.value.filter(
      (item) => (item.stage || item.status) === '已处理',
    ),
  ),
  // 已归档
  archived: computed(() =>
    filteredTicketList.value.filter(
      (item) => (item.stage || item.status) === '已归档',
    ),
  ),
};

const selectedTicketData = computed(() => {
  // 确保有选中的工单ID和工单数据
  if (!selectedTicket.value || !tickets.value || tickets.value.length === 0) {
    return undefined;
  }

  // 尝试精确匹配和字符串匹配，优先使用ticketID
  const ticket = tickets.value.find(
    (item) =>
      item &&
      (item.ticketID || item.id) && // 优先使用ticketID进行匹配
      (item.ticketID === selectedTicket.value ||
        item.ticketID?.toString() === selectedTicket.value ||
        selectedTicket.value === item.ticketID?.toString() ||
        // 兼容性：使用id进行匹配
        item.id === selectedTicket.value ||
        item.id.toString() === selectedTicket.value ||
        selectedTicket.value === item.id.toString()),
  );
  return ticket;
});
const currentTicketList = computed(() => {
  const menu = selectedMenu.value as keyof typeof ticketLists;
  return ticketLists[menu]?.value || ticketLists.all.value;
});

// 监听当前列表变化，更新选中的工单
watch(
  currentTicketList,
  (newList) => {
    try {
      // 只有在没有通过 selectedTicketId prop 指定工单的情况下，才自动选择第一个工单
      const hasSpecifiedTicket = props.selectedTicketId;

      if (
        !hasSpecifiedTicket &&
        newList &&
        Array.isArray(newList) &&
        newList.length > 0 &&
        (!selectedTicket.value ||
          !newList.some(
            (item) =>
              item &&
              (item.id === selectedTicket.value ||
                item.id.toString() === selectedTicket.value ||
                selectedTicket.value === item.id.toString()),
          )) && // 确保newList[0]存在且有id属性
        newList[0] &&
        newList[0].id
      ) {
        selectedTicket.value = newList[0].id;
      }
    } catch (error) {
      console.error('Error in currentTicketList watcher:', error);
    }
  },
  { immediate: true },
);

// 确保指定的工单在当前列表中可见
function ensureTicketVisible(ticketId: string) {
  try {
    // 检查工单是否存在
    const ticket = tickets.value.find(
      (item) =>
        item &&
        (item.id === ticketId ||
          item.id.toString() === ticketId ||
          ticketId === item.id.toString()),
    );

    if (ticket) {
      // 直接访问工单详情页时，统一切换到"所有工单"菜单
      selectedMenu.value = 'all';

      // 通知父组件菜单变化
      emit('update:activeMenu', 'all');
    }
  } catch (error) {
    console.error('Error in ensureTicketVisible:', error);
  }
}
</script>

<template>
  <div>
    <TooltipProvider :delay-duration="0">
      <!-- 高级搜索组件 -->
      <div class="border-b p-4">
        <AdvancedSearch
          @search="handleAdvancedSearch"
          @reset="handleResetSearch"
        />
      </div>

      <!-- 筛选工具栏 - 集成在内容区域 -->
      <div class="border-b">
        <TicketFilterToolbar
          :tickets="currentTicketList"
          v-model:search-value="searchValue"
          @filter="handleFilter"
        />
      </div>
      <ResizablePanelGroup
        id="resize-panel-group-1"
        direction="horizontal"
        class="items-stretch"
      >
        <!-- <ResizablePanel
          id="resize-panel-1"
          :default-size="defaultLayout[0]"
          :collapsed-size="navCollapsedSize"
          collapsible
          :min-size="15"
          :max-size="20"
          :class="
            cn(
              isCollapsed &&
                'min-w-[50px] transition-all duration-300 ease-in-out',
            )
          "
          @expand="onExpand"
          @collapse="onCollapse"
        >
          <div :class="cn('flex h-[52px] items-center justify-center px-2')">
            <Button variant="outline" class="w-full gap-1">
              <Icon name="i-lucide-plus" class="size-4" />
              创建工单
            </Button>
          </div>
          <Separator />
          <Nav
            :is-collapsed="isCollapsed"
            :links="links"
            @menu-click="handleMenuClick"
          />
        </ResizablePanel>
        <ResizableHandle id="resize-handle-1" with-handle />  -->
        <ResizablePanel
          id="resize-panel-2"
          :default-size="defaultLayout[1]"
          :min-size="25"
        >
          <div
            class="bg-background/95 supports-[backdrop-filter]:bg-background/60 backdrop-blur"
          >
            <div class="m-0">
              <TicketList
                v-model:selected-ticket="selectedTicket"
                :items="currentTicketList"
                :search-value="searchValue"
                @view-detail="handleOpenTicketDetail"
                @refresh="handleRefreshTickets"
              />
            </div>
          </div>
        </ResizablePanel>
        <ResizableHandle id="resize-handle-2" with-handle v-if="!isMobile" />
        <ResizablePanel
          id="resize-panel-3"
          :default-size="defaultLayout[2]"
          v-if="!isMobile"
          :min-size="40"
        >
          <TicketDisplay
            :ticket="selectedTicketData"
            v-if="!isMobile || selectedTicket"
          />
        </ResizablePanel>
      </ResizablePanelGroup>
    </TooltipProvider>

    <!-- 工单详情模态框 -->
    <TicketDetailModal
      ref="ticketDetailModalRef"
      @refresh="handleRefreshTickets"
    />
  </div>
</template>
