<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';

import {
  Button,
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@vben-core/shadcn-ui';

import { ChevronDown, ChevronUp, Search, X } from 'lucide-vue-next';

// 搜索过滤器类型
interface SearchFilters {
  keyword?: string;
  status?: string;
  severityLevel?: string;
  functionType?: string;
  creator?: string;
  handler?: string;
  startTime?: string;
  endTime?: string;
  apps?: string;
  osType?: string;
}

// Props
interface AdvancedSearchProps {
  loading?: boolean;
}

const props = withDefaults(defineProps<AdvancedSearchProps>(), {
  loading: false,
});

// Emits
const emit = defineEmits<{
  (e: 'search', filters: SearchFilters): void;
  (e: 'reset'): void;
}>();

// 搜索表单数据
const searchForm = reactive<SearchFilters>({
  keyword: '',
  status: '',
  severityLevel: '',
  functionType: '',
  creator: '',
  handler: '',
  startTime: '',
  endTime: '',
  apps: '',
  osType: '',
});

// 是否展开高级搜索
const isExpanded = ref(false);

// 状态选项
const statusOptions = [
  { value: '', label: '全部状态' },
  { value: '待处理', label: '待处理' },
  { value: '处理中', label: '处理中' },
  { value: '已处理', label: '已处理' },
  { value: '已归档', label: '已归档' },
];

// 严重程度选项
const severityOptions = [
  { value: '', label: '全部程度' },
  { value: '个例问题', label: '个例问题' },
  { value: '批量问题', label: '批量问题' },
  { value: '紧急问题', label: '紧急问题' },
];

// 功能类型选项
const functionTypeOptions = [
  { value: '', label: '全部类型' },
  { value: '房间玩法', label: '房间玩法' },
  { value: '房间音频', label: '房间音频' },
  { value: '直播功能', label: '直播功能' },
  { value: '礼物系统', label: '礼物系统' },
  { value: '游戏功能', label: '游戏功能' },
  { value: '匹配系统', label: '匹配系统' },
  { value: '社交功能', label: '社交功能' },
  { value: '好友系统', label: '好友系统' },
  { value: '账号功能', label: '账号功能' },
  { value: '登录注册', label: '登录注册' },
];

// 应用选项
const appOptions = [
  { value: '', label: '全部应用' },
  { value: 'TT语音', label: 'TT语音' },
  { value: 'TT直播', label: 'TT直播' },
  { value: 'TT游戏', label: 'TT游戏' },
];

// 人员选项（这里应该从API获取）
const userOptions = [
  { value: '', label: '全部人员' },
  { value: 'admin', label: 'admin' },
  { value: '陈婷', label: '陈婷' },
  { value: '王明', label: '王明' },
  { value: '李强', label: '李强' },
  { value: '张伟', label: '张伟' },
  { value: '刘智敏', label: '刘智敏' },
  { value: '卢安', label: '卢安' },
];

// 计算是否有活动的过滤器
const hasActiveFilters = computed(() => {
  return Object.values(searchForm).some(
    (value) => value && value.trim() !== '',
  );
});

// 执行搜索
function handleSearch() {
  // 过滤掉空值
  const filters = Object.fromEntries(
    Object.entries(searchForm).filter(
      ([_, value]) => value && value.trim() !== '',
    ),
  );
  emit('search', filters);
}

// 重置搜索
function handleReset() {
  Object.keys(searchForm).forEach((key) => {
    (searchForm as any)[key] = '';
  });
  emit('reset');
}

// 快速搜索（仅关键词）
function handleQuickSearch() {
  if (searchForm.keyword?.trim()) {
    emit('search', { keyword: searchForm.keyword.trim() });
  } else {
    emit('search', {});
  }
}

// 监听关键词变化，实现实时搜索
watch(
  () => searchForm.keyword,
  (newKeyword) => {
    if (!newKeyword || newKeyword.trim() === '') {
      // 如果关键词为空，触发搜索以显示所有结果
      handleQuickSearch();
    }
  },
);

// 处理回车键搜索
function handleKeywordKeyup(event: KeyboardEvent) {
  if (event.key === 'Enter') {
    handleQuickSearch();
  }
}
</script>

<template>
  <div class="space-y-4">
    <!-- 快速搜索栏 -->
    <div class="flex items-center gap-2">
      <div class="relative flex-1">
        <Search
          class="text-muted-foreground absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2"
        />
        <Input
          v-model="searchForm.keyword"
          placeholder="搜索工单标题、描述、ID..."
          class="pl-9"
          @keyup="handleKeywordKeyup"
        />
      </div>
      <Button @click="handleQuickSearch" :disabled="props.loading">
        搜索
      </Button>
      <Collapsible v-model:open="isExpanded">
        <CollapsibleTrigger as-child>
          <Button variant="outline">
            高级搜索
            <ChevronDown v-if="!isExpanded" class="ml-1 h-4 w-4" />
            <ChevronUp v-else class="ml-1 h-4 w-4" />
          </Button>
        </CollapsibleTrigger>
      </Collapsible>
    </div>

    <!-- 高级搜索面板 -->
    <Collapsible v-model:open="isExpanded">
      <CollapsibleContent class="space-y-4">
        <div class="space-y-4 rounded-lg border p-4">
          <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            <!-- 状态筛选 -->
            <div class="space-y-2">
              <Label class="text-sm font-medium">状态</Label>
              <Select v-model="searchForm.status">
                <SelectTrigger>
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="option in statusOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- 严重程度筛选 -->
            <div class="space-y-2">
              <Label class="text-sm font-medium">严重程度</Label>
              <Select v-model="searchForm.severityLevel">
                <SelectTrigger>
                  <SelectValue placeholder="选择严重程度" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="option in severityOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- 功能类型筛选 -->
            <div class="space-y-2">
              <Label class="text-sm font-medium">功能类型</Label>
              <Select v-model="searchForm.functionType">
                <SelectTrigger>
                  <SelectValue placeholder="选择功能类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="option in functionTypeOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- 创建人筛选 -->
            <div class="space-y-2">
              <Label class="text-sm font-medium">创建人</Label>
              <Select v-model="searchForm.creator">
                <SelectTrigger>
                  <SelectValue placeholder="选择创建人" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="option in userOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- 处理人筛选 -->
            <div class="space-y-2">
              <Label class="text-sm font-medium">处理人</Label>
              <Select v-model="searchForm.handler">
                <SelectTrigger>
                  <SelectValue placeholder="选择处理人" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="option in userOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- 应用筛选 -->
            <div class="space-y-2">
              <Label class="text-sm font-medium">应用</Label>
              <Select v-model="searchForm.apps">
                <SelectTrigger>
                  <SelectValue placeholder="选择应用" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="option in appOptions"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center justify-between border-t pt-4">
            <div class="flex items-center gap-2">
              <Button @click="handleSearch" :disabled="props.loading">
                <Search class="mr-1 h-4 w-4" />
                搜索
              </Button>
              <Button
                variant="outline"
                @click="handleReset"
                :disabled="props.loading || !hasActiveFilters"
              >
                <X class="mr-1 h-4 w-4" />
                重置
              </Button>
            </div>
            <div v-if="hasActiveFilters" class="text-muted-foreground text-sm">
              已设置
              {{
                Object.values(searchForm).filter((v) => v && v.trim() !== '')
                  .length
              }}
              个筛选条件
            </div>
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  </div>
</template>
